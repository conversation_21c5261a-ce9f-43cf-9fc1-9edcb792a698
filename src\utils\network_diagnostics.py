"""
Network Diagnostics and Connectivity Testing for MongoDB Atlas
Diagnoses DNS, network, and connectivity issues with comprehensive troubleshooting
"""

import asyncio
import socket
import time
import logging
import dns.resolver
import dns.exception
from typing import Dict, List, Optional, Tuple
from urllib.parse import urlparse
import subprocess
import platform

logger = logging.getLogger(__name__)

class NetworkDiagnostics:
    """Comprehensive network diagnostics for MongoDB Atlas connectivity"""
    
    def __init__(self):
        self.dns_servers = [
            '8.8.8.8',      # Google DNS
            '8.8.4.4',      # Google DNS Secondary
            '1.1.1.1',      # Cloudflare DNS
            '1.0.0.1',      # Cloudflare DNS Secondary
            '208.67.222.222',  # OpenDNS
            '208.67.220.220',  # OpenDNS Secondary
        ]
        
        self.mongodb_atlas_domains = [
            'cluster0.mongodb.net',
            'mongodb.net',
            'mongodb.com'
        ]
    
    async def diagnose_connection_issue(self, mongodb_uri: str) -> Dict[str, any]:
        """Comprehensive diagnosis of MongoDB Atlas connection issues"""
        logger.info("🔍 Starting comprehensive network diagnostics...")
        
        diagnosis = {
            'timestamp': time.time(),
            'mongodb_uri_parsed': self._parse_mongodb_uri(mongodb_uri),
            'dns_resolution': {},
            'network_connectivity': {},
            'mongodb_specific': {},
            'system_info': {},
            'recommendations': []
        }
        
        try:
            # Parse MongoDB URI
            parsed_uri = self._parse_mongodb_uri(mongodb_uri)
            if not parsed_uri['valid']:
                diagnosis['recommendations'].append("❌ Invalid MongoDB URI format")
                return diagnosis
            
            # System information
            diagnosis['system_info'] = await self._get_system_info()
            
            # DNS resolution tests
            diagnosis['dns_resolution'] = await self._test_dns_resolution(parsed_uri['hosts'])
            
            # Network connectivity tests
            diagnosis['network_connectivity'] = await self._test_network_connectivity(parsed_uri['hosts'])
            
            # MongoDB-specific tests
            diagnosis['mongodb_specific'] = await self._test_mongodb_connectivity(mongodb_uri)
            
            # Generate recommendations
            diagnosis['recommendations'] = self._generate_recommendations(diagnosis)
            
            return diagnosis
            
        except Exception as e:
            logger.error(f"Error during network diagnosis: {e}")
            diagnosis['error'] = str(e)
            return diagnosis
    
    def _parse_mongodb_uri(self, uri: str) -> Dict[str, any]:
        """Parse MongoDB URI to extract connection details"""
        try:
            if not uri.startswith('mongodb'):
                return {'valid': False, 'error': 'URI must start with mongodb:// or mongodb+srv://'}
            
            # Handle mongodb+srv:// URIs
            if uri.startswith('mongodb+srv://'):
                # Extract hostname from SRV URI
                parsed = urlparse(uri)
                hostname = parsed.hostname
                port = 27017  # Default MongoDB port
                
                return {
                    'valid': True,
                    'type': 'srv',
                    'hosts': [(hostname, port)],
                    'hostname': hostname,
                    'database': parsed.path.lstrip('/').split('?')[0] if parsed.path else None,
                    'username': parsed.username,
                    'srv_domain': hostname
                }
            
            # Handle regular mongodb:// URIs
            elif uri.startswith('mongodb://'):
                parsed = urlparse(uri)
                hosts = []
                
                if parsed.hostname:
                    port = parsed.port or 27017
                    hosts.append((parsed.hostname, port))
                
                return {
                    'valid': True,
                    'type': 'standard',
                    'hosts': hosts,
                    'hostname': parsed.hostname,
                    'port': parsed.port or 27017,
                    'database': parsed.path.lstrip('/').split('?')[0] if parsed.path else None,
                    'username': parsed.username
                }
            
            return {'valid': False, 'error': 'Unsupported URI format'}
            
        except Exception as e:
            return {'valid': False, 'error': f'URI parsing error: {str(e)}'}
    
    async def _get_system_info(self) -> Dict[str, any]:
        """Get system network information"""
        try:
            info = {
                'platform': platform.system(),
                'platform_version': platform.version(),
                'python_version': platform.python_version(),
                'hostname': socket.gethostname(),
                'default_dns_servers': [],
                'network_interfaces': []
            }
            
            # Get default DNS servers (Windows)
            if platform.system() == 'Windows':
                try:
                    result = subprocess.run(['nslookup', 'google.com'], 
                                          capture_output=True, text=True, timeout=10)
                    if result.stdout:
                        lines = result.stdout.split('\n')
                        for line in lines:
                            if 'Server:' in line:
                                dns_server = line.split(':')[-1].strip()
                                if dns_server and dns_server != 'UnKnown':
                                    info['default_dns_servers'].append(dns_server)
                except Exception:
                    pass
            
            return info
            
        except Exception as e:
            return {'error': f'Failed to get system info: {str(e)}'}
    
    async def _test_dns_resolution(self, hosts: List[Tuple[str, int]]) -> Dict[str, any]:
        """Test DNS resolution for MongoDB hosts"""
        results = {
            'status': 'unknown',
            'hosts_resolved': {},
            'dns_server_tests': {},
            'srv_record_tests': {},
            'resolution_times': {}
        }
        
        try:
            for hostname, port in hosts:
                logger.info(f"🔍 Testing DNS resolution for {hostname}")
                
                # Test with system DNS
                host_result = await self._resolve_hostname(hostname)
                results['hosts_resolved'][hostname] = host_result
                
                # Test with alternative DNS servers
                dns_tests = {}
                for dns_server in self.dns_servers[:3]:  # Test top 3 DNS servers
                    dns_result = await self._resolve_with_dns_server(hostname, dns_server)
                    dns_tests[dns_server] = dns_result
                
                results['dns_server_tests'][hostname] = dns_tests
                
                # Test SRV records for MongoDB Atlas
                if 'mongodb.net' in hostname:
                    srv_result = await self._test_srv_records(hostname)
                    results['srv_record_tests'][hostname] = srv_result
            
            # Determine overall status
            successful_resolutions = sum(1 for host_data in results['hosts_resolved'].values() 
                                       if host_data.get('resolved', False))
            
            if successful_resolutions == len(hosts):
                results['status'] = 'success'
            elif successful_resolutions > 0:
                results['status'] = 'partial'
            else:
                results['status'] = 'failed'
            
            return results
            
        except Exception as e:
            results['error'] = str(e)
            results['status'] = 'error'
            return results
    
    async def _resolve_hostname(self, hostname: str) -> Dict[str, any]:
        """Resolve hostname using system DNS"""
        try:
            start_time = time.time()
            
            # Use asyncio to resolve hostname
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, socket.gethostbyname, hostname)
            
            resolution_time = (time.time() - start_time) * 1000  # Convert to ms
            
            return {
                'resolved': True,
                'ip_address': result,
                'resolution_time_ms': resolution_time,
                'method': 'system_dns'
            }
            
        except socket.gaierror as e:
            return {
                'resolved': False,
                'error': f'DNS resolution failed: {str(e)}',
                'error_code': e.errno if hasattr(e, 'errno') else None
            }
        except Exception as e:
            return {
                'resolved': False,
                'error': f'Resolution error: {str(e)}'
            }
    
    async def _resolve_with_dns_server(self, hostname: str, dns_server: str) -> Dict[str, any]:
        """Resolve hostname using specific DNS server"""
        try:
            resolver = dns.resolver.Resolver()
            resolver.nameservers = [dns_server]
            resolver.timeout = 10
            resolver.lifetime = 10
            
            start_time = time.time()
            
            # Run DNS query in executor to avoid blocking
            loop = asyncio.get_event_loop()
            answers = await loop.run_in_executor(None, resolver.resolve, hostname, 'A')
            
            resolution_time = (time.time() - start_time) * 1000
            
            ip_addresses = [str(answer) for answer in answers]
            
            return {
                'resolved': True,
                'ip_addresses': ip_addresses,
                'resolution_time_ms': resolution_time,
                'dns_server': dns_server
            }
            
        except dns.exception.Timeout:
            return {
                'resolved': False,
                'error': f'DNS timeout using server {dns_server}',
                'dns_server': dns_server
            }
        except Exception as e:
            return {
                'resolved': False,
                'error': f'DNS error with {dns_server}: {str(e)}',
                'dns_server': dns_server
            }
    
    async def _test_srv_records(self, hostname: str) -> Dict[str, any]:
        """Test SRV records for MongoDB Atlas"""
        try:
            resolver = dns.resolver.Resolver()
            resolver.timeout = 10
            
            # MongoDB SRV record format
            srv_hostname = f"_mongodb._tcp.{hostname}"
            
            start_time = time.time()
            loop = asyncio.get_event_loop()
            answers = await loop.run_in_executor(None, resolver.resolve, srv_hostname, 'SRV')
            resolution_time = (time.time() - start_time) * 1000
            
            srv_records = []
            for answer in answers:
                srv_records.append({
                    'target': str(answer.target),
                    'port': answer.port,
                    'priority': answer.priority,
                    'weight': answer.weight
                })
            
            return {
                'resolved': True,
                'srv_records': srv_records,
                'resolution_time_ms': resolution_time
            }
            
        except Exception as e:
            return {
                'resolved': False,
                'error': f'SRV record resolution failed: {str(e)}'
            }
    
    async def _test_network_connectivity(self, hosts: List[Tuple[str, int]]) -> Dict[str, any]:
        """Test network connectivity to MongoDB hosts"""
        results = {
            'status': 'unknown',
            'host_connectivity': {},
            'ping_tests': {},
            'port_tests': {}
        }
        
        try:
            for hostname, port in hosts:
                logger.info(f"🔍 Testing network connectivity to {hostname}:{port}")
                
                # Test ping connectivity
                ping_result = await self._test_ping(hostname)
                results['ping_tests'][hostname] = ping_result
                
                # Test port connectivity
                port_result = await self._test_port_connectivity(hostname, port)
                results['port_tests'][f"{hostname}:{port}"] = port_result
                
                # Overall connectivity for this host
                results['host_connectivity'][hostname] = {
                    'ping_success': ping_result.get('success', False),
                    'port_open': port_result.get('connected', False),
                    'overall_status': 'success' if (ping_result.get('success', False) and 
                                                   port_result.get('connected', False)) else 'failed'
                }
            
            # Determine overall status
            successful_connections = sum(1 for host_data in results['host_connectivity'].values() 
                                       if host_data['overall_status'] == 'success')
            
            if successful_connections == len(hosts):
                results['status'] = 'success'
            elif successful_connections > 0:
                results['status'] = 'partial'
            else:
                results['status'] = 'failed'
            
            return results
            
        except Exception as e:
            results['error'] = str(e)
            results['status'] = 'error'
            return results
    
    async def _test_ping(self, hostname: str) -> Dict[str, any]:
        """Test ping connectivity to hostname"""
        try:
            # Use platform-appropriate ping command
            if platform.system() == 'Windows':
                cmd = ['ping', '-n', '3', hostname]
            else:
                cmd = ['ping', '-c', '3', hostname]
            
            start_time = time.time()
            result = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await asyncio.wait_for(result.communicate(), timeout=15)
            ping_time = (time.time() - start_time) * 1000
            
            success = result.returncode == 0
            
            return {
                'success': success,
                'ping_time_ms': ping_time,
                'output': stdout.decode() if stdout else '',
                'error': stderr.decode() if stderr else ''
            }
            
        except asyncio.TimeoutError:
            return {
                'success': False,
                'error': 'Ping timeout after 15 seconds'
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'Ping failed: {str(e)}'
            }
    
    async def _test_port_connectivity(self, hostname: str, port: int) -> Dict[str, any]:
        """Test TCP port connectivity"""
        try:
            start_time = time.time()
            
            # Test TCP connection
            reader, writer = await asyncio.wait_for(
                asyncio.open_connection(hostname, port),
                timeout=10
            )
            
            connection_time = (time.time() - start_time) * 1000
            
            # Close connection
            writer.close()
            await writer.wait_closed()
            
            return {
                'connected': True,
                'connection_time_ms': connection_time,
                'port': port
            }
            
        except asyncio.TimeoutError:
            return {
                'connected': False,
                'error': f'Connection timeout to {hostname}:{port}',
                'port': port
            }
        except Exception as e:
            return {
                'connected': False,
                'error': f'Connection failed to {hostname}:{port}: {str(e)}',
                'port': port
            }
    
    async def _test_mongodb_connectivity(self, mongodb_uri: str) -> Dict[str, any]:
        """Test MongoDB-specific connectivity"""
        try:
            from motor.motor_asyncio import AsyncIOMotorClient
            
            # Test with minimal timeout settings
            client = AsyncIOMotorClient(
                mongodb_uri,
                serverSelectionTimeoutMS=15000,  # 15 seconds
                connectTimeoutMS=15000,
                socketTimeoutMS=15000
            )
            
            start_time = time.time()
            
            # Test basic connection
            await asyncio.wait_for(client.admin.command('ping'), timeout=20)
            
            connection_time = (time.time() - start_time) * 1000
            
            # Get server info
            server_info = await client.admin.command('buildInfo')
            
            client.close()
            
            return {
                'connected': True,
                'connection_time_ms': connection_time,
                'server_version': server_info.get('version', 'unknown'),
                'server_info': {
                    'version': server_info.get('version'),
                    'gitVersion': server_info.get('gitVersion'),
                    'platform': server_info.get('platform')
                }
            }
            
        except asyncio.TimeoutError:
            return {
                'connected': False,
                'error': 'MongoDB connection timeout after 20 seconds'
            }
        except Exception as e:
            return {
                'connected': False,
                'error': f'MongoDB connection failed: {str(e)}'
            }
    
    def _generate_recommendations(self, diagnosis: Dict[str, any]) -> List[str]:
        """Generate troubleshooting recommendations based on diagnosis"""
        recommendations = []
        
        # DNS-related recommendations
        dns_status = diagnosis.get('dns_resolution', {}).get('status')
        if dns_status == 'failed':
            recommendations.extend([
                "🔧 DNS Resolution Failed:",
                "  • Try using alternative DNS servers (8.8.8.8, 1.1.1.1)",
                "  • Check if your ISP is blocking MongoDB Atlas domains",
                "  • Verify your network connection is stable",
                "  • Try connecting from a different network (mobile hotspot)"
            ])
        elif dns_status == 'partial':
            recommendations.append("⚠️ Partial DNS resolution - some DNS servers may be blocked")
        
        # Network connectivity recommendations
        network_status = diagnosis.get('network_connectivity', {}).get('status')
        if network_status == 'failed':
            recommendations.extend([
                "🔧 Network Connectivity Failed:",
                "  • Check if your firewall is blocking MongoDB ports (27017)",
                "  • Verify your IP is whitelisted in MongoDB Atlas",
                "  • Check if your ISP or corporate network blocks MongoDB",
                "  • Try connecting from a different network"
            ])
        
        # MongoDB-specific recommendations
        mongodb_status = diagnosis.get('mongodb_specific', {}).get('connected')
        if mongodb_status is False:
            error = diagnosis.get('mongodb_specific', {}).get('error', '')
            if 'timeout' in error.lower():
                recommendations.extend([
                    "🔧 MongoDB Connection Timeout:",
                    "  • Increase connection timeout settings",
                    "  • Check MongoDB Atlas cluster status",
                    "  • Verify cluster is not paused or sleeping",
                    "  • Check if your IP is whitelisted (0.0.0.0/0 for testing)"
                ])
            elif 'authentication' in error.lower():
                recommendations.extend([
                    "🔧 MongoDB Authentication Failed:",
                    "  • Verify username and password in connection string",
                    "  • Check if database user has proper permissions",
                    "  • Ensure database name is correct"
                ])
        
        # General recommendations
        if not recommendations:
            recommendations.append("✅ All connectivity tests passed - connection should work")
        else:
            recommendations.extend([
                "",
                "🔧 General Troubleshooting:",
                "  • Try restarting your network adapter",
                "  • Flush DNS cache (ipconfig /flushdns on Windows)",
                "  • Check MongoDB Atlas status page",
                "  • Contact your network administrator if on corporate network"
            ])
        
        return recommendations

# Global diagnostics instance
network_diagnostics = NetworkDiagnostics()
