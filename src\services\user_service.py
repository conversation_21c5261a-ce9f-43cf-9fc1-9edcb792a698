"""
User service for managing user operations
"""

import logging
from datetime import datetime, timezone
from typing import Optional, List, Dict, Any
from pymongo.errors import DuplicateKeyError, ConnectionFailure, ServerSelectionTimeoutError, OperationFailure
import asyncio

from src.database import Database
from src.models.user import User
from config import Config


logger = logging.getLogger(__name__)

def retry_on_connection_error(max_retries: int = 3, delay: float = 1.0):
    """Decorator to retry database operations on connection errors"""
    def decorator(func):
        async def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(max_retries):
                try:
                    return await func(*args, **kwargs)
                except (ConnectionFailure, ServerSelectionTimeoutError) as e:
                    last_exception = e
                    if attempt < max_retries - 1:
                        logger.warning(f"Database connection error on attempt {attempt + 1}, retrying in {delay}s: {e}")
                        await asyncio.sleep(delay * (attempt + 1))  # Exponential backoff
                    else:
                        logger.error(f"Database connection failed after {max_retries} attempts: {e}")
                        raise
                except Exception as e:
                    # Don't retry on other types of exceptions
                    raise
            raise last_exception
        return wrapper
    return decorator

class UserService:
    """Service for user management operations"""

    def __init__(self, database: Database):
        self.db = database
    
    async def create_user(self, user_data: Dict[str, Any], referral_code: Optional[str] = None) -> Optional[User]:
        """
        Create a new user with atomic transaction support.

        Args:
            user_data (Dict[str, Any]): User data from Telegram API containing:
                - user_id (int): Telegram user ID
                - username (str, optional): Telegram username
                - first_name (str): User's first name
                - last_name (str, optional): User's last name
                - language_code (str, optional): User's language preference
                - is_bot (bool): Whether the user is a bot
                - is_premium (bool): Whether the user has Telegram Premium
            referral_code (str, optional): Referral code if user was referred

        Returns:
            Optional[User]: Created user object or None if creation failed

        Raises:
            ValueError: If user_data is invalid
            OperationFailure: If database operation fails

        Note:
            - Uses MongoDB transactions for atomic user creation and welcome bonus
            - Automatically applies friend welcome bonus if referral_code provided
            - Sets referrer relationship if valid referral code provided
        """
        async with await self.db.client.start_session() as session:
            try:
                logger.info(f"🔄 UserService.create_user called for user {user_data.get('user_id')}")

                # Check if user already exists
                existing_user = await self.get_user(user_data['user_id'])
                if existing_user:
                    logger.info(f"👤 User {user_data['user_id']} already exists, returning existing user")
                    return existing_user

                logger.info(f"🆕 Creating new user {user_data['user_id']}")

                async with session.start_transaction():
                    # Create user object
                    from config import Config
                    user = User(
                        user_id=user_data['user_id'],
                        username=user_data.get('username'),
                        first_name=user_data.get('first_name'),
                        last_name=user_data.get('last_name'),
                        language_code=user_data.get('language_code'),
                        is_bot=user_data.get('is_bot', False),
                        is_premium=user_data.get('is_premium', False)
                    )

                    welcome_transaction = None
                    # Apply friend welcome bonus if user was referred
                    if referral_code:
                        from src.models.transaction import Transaction, TransactionType, TransactionStatus
                        welcome_bonus = Config.FRIEND_WELCOME_BONUS
                        if welcome_bonus > 0:
                            user.add_balance(welcome_bonus, f"Friend Welcome Bonus: {welcome_bonus} Genesis Tokens")

                            # Create transaction record for friend welcome bonus
                            welcome_transaction = Transaction(
                                user_id=user.user_id,
                                amount=welcome_bonus,
                                transaction_type=TransactionType.WELCOME_BONUS,
                                status=TransactionStatus.COMPLETED,
                                description=f"Friend Welcome Bonus: {welcome_bonus} Genesis Tokens for joining via referral",
                                reference_id=referral_code
                            )

                    logger.info(f"📋 User object created: {user.user_id}")

                    # Set referrer if referral code provided
                    if referral_code:
                        logger.info(f"🔗 Processing referral code: {referral_code}")
                        referrer = await self.get_user_by_referral_code(referral_code)
                        if referrer and referrer.user_id != user.user_id:
                            user.referred_by = referrer.user_id
                            logger.info(f"✅ Set referrer: {referrer.user_id} for user {user.user_id}")
                        else:
                            logger.info(f"❌ No valid referrer found for code: {referral_code}")

                    # Save user to database within transaction
                    logger.info(f"💾 Saving user {user.user_id} to database...")
                    user_dict = user.to_dict()
                    logger.info(f"📊 User dict: {user_dict}")

                    result = await self.db.db.users.insert_one(user_dict, session=session)

                    # Save welcome bonus transaction if applicable
                    if welcome_transaction:
                        await self.db.transactions.insert_one(welcome_transaction.to_dict(), session=session)
                        logger.info(f"Applied friend welcome bonus of {Config.FRIEND_WELCOME_BONUS} Genesis Tokens to new user {user.user_id}")

                    # Commit transaction
                    await session.commit_transaction()

                    logger.info(f"✅ Database insert result: {result.inserted_id}")
                    logger.info(f"✅ Atomically created new user: {user.user_id}")

                    return user

            except DuplicateKeyError:
                # User already exists, return existing user
                try:
                    await session.abort_transaction()
                except:
                    pass
                return await self.get_user(user_data['user_id'])
            except Exception as e:
                logger.error(f"Failed to create user {user_data.get('user_id')}: {e}")
                try:
                    await session.abort_transaction()
                except:
                    pass
                return None
    
    @retry_on_connection_error(max_retries=3)
    async def get_user(self, user_id: int) -> Optional[User]:
        """
        Get user by ID with direct database access for real-time data.

        Args:
            user_id (int): Telegram user ID (must be positive integer)

        Returns:
            Optional[User]: User object if found, None otherwise

        Raises:
            ValueError: If user_id is invalid (not positive integer)
            OperationFailure: If database operation fails
            ConnectionFailure: If database connection fails (auto-retried)

        Note:
            - Includes automatic retry logic for connection failures
            - Validates user_id format before database query
            - Returns real-time data directly from database
        """
        try:
            if not isinstance(user_id, int) or user_id <= 0:
                raise ValueError(f"Invalid user_id: {user_id}")

            # Get directly from database for real-time consistency
            user_data = await self.db.db.users.find_one({'user_id': user_id})
            if user_data is not None:
                return User.from_dict(user_data)
            return None
        except ValueError as e:
            logger.error(f"Invalid user ID provided: {e}")
            return None
        except OperationFailure as e:
            logger.error(f"Database operation failed for user {user_id}: {e}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error getting user {user_id}: {e}")
            return None

    async def get_user_by_referral_code(self, referral_code: str) -> Optional[User]:
        """Get user by referral code"""
        try:
            user_data = await self.db.db.users.find_one({'referral_code': referral_code})
            if user_data is not None:
                return User.from_dict(user_data)
            return None
        except Exception as e:
            logger.error(f"Failed to get user by referral code {referral_code}: {e}")
            return None

    async def update_user(self, user: User) -> bool:
        """Update user in database with real-time consistency"""
        try:
            user.update_activity()
            result = await self.db.db.users.update_one(
                {'user_id': user.user_id},
                {'$set': user.to_dict()}
            )

            return result.modified_count > 0
        except Exception as e:
            logger.error(f"Failed to update user {user.user_id}: {e}")
            return False
    
    @retry_on_connection_error(max_retries=3)
    async def update_user_balance(self, user_id: int, amount: int, reason: str = "") -> bool:
        """
        Update user balance with comprehensive validation and atomic operations.

        Args:
            user_id (int): Telegram user ID (must be positive integer)
            amount (int): Amount to add/subtract (positive to add, negative to subtract)
            reason (str, optional): Reason for balance change (for audit trail)

        Returns:
            bool: True if balance updated successfully, False otherwise

        Raises:
            ValueError: If user_id or amount is invalid
            OperationFailure: If database operation fails
            ConnectionFailure: If database connection fails (auto-retried)

        Note:
            - Validates amount is integer and within reasonable limits (±1,000,000)
            - Prevents negative balance operations (insufficient funds)
            - Includes automatic retry logic for connection failures
            - Logs balance changes for audit trail
        """
        try:
            # Input validation
            if not isinstance(user_id, int) or user_id <= 0:
                raise ValueError(f"Invalid user_id: {user_id}")
            if not isinstance(amount, int):
                raise ValueError("Amount must be an integer")
            if amount == 0:
                raise ValueError("Amount cannot be zero")
            if abs(amount) > 1000000:  # Reasonable limit
                raise ValueError(f"Amount too large: {amount}")

            user = await self.get_user(user_id)
            if not user:
                logger.warning(f"User {user_id} not found for balance update")
                return False

            # Check for negative balance operations
            if amount < 0 and user.balance < abs(amount):
                logger.warning(f"Insufficient balance for user {user_id}: has {user.balance}, needs {abs(amount)}")
                return False

            if amount > 0:
                user.add_balance(amount, reason)
            else:
                if not user.deduct_balance(abs(amount)):
                    logger.warning(f"Failed to deduct balance for user {user_id}")
                    return False

            result = await self.update_user(user)

            if result:
                logger.info(f"Updated balance for user {user_id}: {amount:+d} Genesis Tokens (new balance: {user.balance})")
                return result
            return False

        except ValueError as e:
            logger.error(f"Validation error for user {user_id}: {e}")
            return False
        except OperationFailure as e:
            logger.error(f"Database operation failed for user {user_id}: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error updating balance for user {user_id}: {e}")
            return False
    
    async def ban_user(self, user_id: int, reason: str = "", admin_id: Optional[int] = None) -> bool:
        """Ban a user"""
        try:
            user = await self.get_user(user_id)
            if not user:
                return False
            
            user.ban_user(reason)
            result = await self.update_user(user)

            return result
            
        except Exception as e:
            logger.error(f"Failed to ban user {user_id}: {e}")
            return False
    
    async def unban_user(self, user_id: int, admin_id: Optional[int] = None) -> bool:
        """Unban a user"""
        try:
            user = await self.get_user(user_id)
            if not user:
                return False
            
            user.unban_user()
            result = await self.update_user(user)

            return result
            
        except Exception as e:
            logger.error(f"Failed to unban user {user_id}: {e}")
            return False
    
    async def get_users_count(self) -> int:
        """Get total number of users"""
        try:
            return await self.db.db.users.count_documents({})
        except Exception as e:
            logger.error(f"Failed to get users count: {e}")
            return 0

    async def get_active_users_count(self) -> int:
        """Get number of active users"""
        try:
            return await self.db.db.users.count_documents({'is_active': True, 'is_banned': False})
        except Exception as e:
            logger.error(f"Failed to get active users count: {e}")
            return 0

    async def get_users_paginated(self, page: int = 0, per_page: int = 20) -> List[User]:
        """Get users with pagination"""
        try:
            skip = page * per_page
            cursor = self.db.db.users.find().sort('created_at', -1).skip(skip).limit(per_page)
            users = []

            async for user_data in cursor:
                users.append(User.from_dict(user_data))

            return users

        except Exception as e:
            logger.error(f"Failed to get paginated users: {e}")
            return []
    
    async def search_users(self, query: str, limit: int = 20) -> List[User]:
        """Search users by username, first name, or user ID"""
        try:
            # Try to search by user ID if query is numeric
            search_filters = []

            if query.isdigit():
                search_filters.append({'user_id': int(query)})

            # Search by username and name
            search_filters.extend([
                {'username': {'$regex': query, '$options': 'i'}},
                {'first_name': {'$regex': query, '$options': 'i'}},
                {'last_name': {'$regex': query, '$options': 'i'}}
            ])

            cursor = self.db.db.users.find({'$or': search_filters}).limit(limit)
            users = []

            async for user_data in cursor:
                users.append(User.from_dict(user_data))

            return users

        except Exception as e:
            logger.error(f"Failed to search users: {e}")
            return []

    async def cleanup_incomplete_registrations(self, max_age_minutes: int = 30) -> int:
        """Remove users who didn't complete registration within the specified time"""
        try:
            from datetime import datetime, timezone, timedelta

            # Calculate cutoff time
            cutoff_time = datetime.now(timezone.utc) - timedelta(minutes=max_age_minutes)

            # Find incomplete registrations older than cutoff
            query = {
                'registration_completed': {'$ne': True},  # Not completed or field doesn't exist
                'created_at': {'$lt': cutoff_time}
            }

            # Count documents to be deleted
            count = await self.db.db.users.count_documents(query)

            if count > 0:
                # Delete incomplete registrations
                result = await self.db.db.users.delete_many(query)
                logger.info(f"Cleaned up {result.deleted_count} incomplete user registrations")
                return result.deleted_count

            return 0

        except Exception as e:
            logger.error(f"Failed to cleanup incomplete registrations: {e}")
            return 0

    async def mark_registration_completed(self, user_id: int) -> bool:
        """Mark user registration as completed"""
        try:
            user = await self.get_user(user_id)
            if user:
                user.complete_registration()
                return await self.update_user(user)
            return False
        except Exception as e:
            logger.error(f"Failed to mark registration completed for user {user_id}: {e}")
            return False
    
    async def get_top_referrers(self, limit: int = 10) -> List[User]:
        """Get top users by referral count (legacy method)"""
        return await self.get_leaderboard(page=0, per_page=limit, sort_by='referral_count')

    @retry_on_connection_error(max_retries=3)
    async def get_leaderboard(self, page: int = 0, per_page: int = 10, sort_by: str = 'balance') -> List[User]:
        """
        Get leaderboard with pagination support.

        Args:
            page (int): Page number (0-based)
            per_page (int): Number of users per page (max 50)
            sort_by (str): Sort field ('balance', 'referral_count', 'total_earned', 'referral')

        Returns:
            List[User]: List of users for the requested page

        Raises:
            ValueError: If parameters are invalid
            OperationFailure: If database operation fails

        Note:
            - Supports pagination for large user bases
            - Multiple sort options for different leaderboard types
            - Excludes banned and inactive users
            - 'referral' is mapped to 'referral_count' for compatibility
        """
        try:
            # Map sort_by aliases to actual field names
            sort_field_mapping = {
                'balance': 'balance',
                'referral_count': 'referral_count',
                'referral': 'referral_count',  # Map 'referral' to 'referral_count'
                'total_earned': 'total_earned',
                'successful_referrals': 'successful_referrals'
            }

            # Validate parameters
            if not isinstance(page, int) or page < 0:
                raise ValueError(f"Invalid page number: {page}")
            if not isinstance(per_page, int) or per_page < 1 or per_page > 50:
                raise ValueError(f"Invalid per_page value: {per_page} (must be 1-50)")
            if sort_by not in sort_field_mapping:
                raise ValueError(f"Invalid sort_by field: {sort_by}. Valid options: {list(sort_field_mapping.keys())}")

            # Get the actual database field name
            actual_sort_field = sort_field_mapping[sort_by]

            # Calculate skip value
            skip = page * per_page

            # Build query filter (exclude banned/inactive users)
            query_filter = {
                'is_banned': False,
                'is_active': True,
                actual_sort_field: {'$gt': 0}  # Only include users with positive values
            }

            # Execute query with pagination
            cursor = self.db.db.users.find(query_filter).sort(actual_sort_field, -1).skip(skip).limit(per_page)
            users = []

            async for user_data in cursor:
                users.append(User.from_dict(user_data))

            logger.debug(f"Retrieved {len(users)} users for leaderboard page {page} (sort: {sort_by} -> {actual_sort_field})")
            return users

        except ValueError as e:
            logger.error(f"Invalid leaderboard parameters: {e}")
            return []
        except OperationFailure as e:
            logger.error(f"Database operation failed for leaderboard: {e}")
            return []
        except Exception as e:
            logger.error(f"Unexpected error getting leaderboard: {e}")
            return []

    async def get_leaderboard_total_pages(self, per_page: int = 10, sort_by: str = 'balance') -> int:
        """
        Get total number of pages for leaderboard pagination.

        Args:
            per_page (int): Number of users per page
            sort_by (str): Sort field for filtering

        Returns:
            int: Total number of pages
        """
        try:
            # Map sort_by aliases to actual field names
            sort_field_mapping = {
                'balance': 'balance',
                'referral_count': 'referral_count',
                'referral': 'referral_count',  # Map 'referral' to 'referral_count'
                'total_earned': 'total_earned',
                'successful_referrals': 'successful_referrals'
            }

            if sort_by not in sort_field_mapping:
                return 0

            actual_sort_field = sort_field_mapping[sort_by]

            query_filter = {
                'is_banned': False,
                'is_active': True,
                actual_sort_field: {'$gt': 0}
            }

            total_count = await self.db.db.users.count_documents(query_filter)
            return (total_count + per_page - 1) // per_page  # Ceiling division

        except Exception as e:
            logger.error(f"Failed to get leaderboard total pages: {e}")
            return 0

    async def update_user_channel_status(self, user_id: int, channel_id: str, joined: bool) -> bool:
        """Update user's channel join status"""
        try:
            user = await self.get_user(user_id)
            if not user:
                return False
            
            if joined:
                user.join_channel(channel_id)
            else:
                user.leave_channel(channel_id)
            
            return await self.update_user(user)
            
        except Exception as e:
            logger.error(f"Failed to update channel status for user {user_id}: {e}")
            return False
    
    async def get_user_statistics(self, user_id: int) -> Dict[str, Any]:
        """Get comprehensive user statistics"""
        try:
            user = await self.get_user(user_id)
            if not user:
                return {}
            
            # Get referral count
            referral_count = await self.db.db.referrals.count_documents({'referrer_id': user_id})

            # Get transaction count
            transaction_count = await self.db.db.transactions.count_documents({'user_id': user_id})

            # Get withdrawal count
            withdrawal_count = await self.db.db.withdrawals.count_documents({'user_id': user_id})
            
            return {
                'user': user.to_dict(),
                'referral_count': referral_count,
                'transaction_count': transaction_count,
                'withdrawal_count': withdrawal_count,
                'days_since_join': (datetime.now(timezone.utc) - user.created_at).days,
                'can_withdraw': user.balance >= Config.MINIMUM_WITHDRAWAL
            }
            
        except Exception as e:
            logger.error(f"Failed to get user statistics for {user_id}: {e}")
            return {}

    async def add_balance(self, user_id: int, amount: float) -> bool:
        """Add balance to user account"""
        try:
            user = await self.get_user(user_id)
            if user:
                user.add_balance(amount, "Balance addition")
                return await self.update_user(user)
            return False
        except Exception as e:
            logger.error(f"Failed to add balance to user {user_id}: {e}")
            return False

    async def update_last_daily_claim(self, user_id: int) -> bool:
        """Update user's last daily claim time"""
        try:
            user = await self.get_user(user_id)
            if user:
                user.last_daily_bonus = datetime.now(timezone.utc)
                return await self.update_user(user)
            return False
        except Exception as e:
            logger.error(f"Failed to update last daily claim for user {user_id}: {e}")
            return False



    async def claim_daily_bonus_concurrent(self, user_id: int, bonus_amount: float) -> bool:
        """Claim daily bonus with user-level locking to prevent double claims"""
        return await self.concurrent_ops.execute_daily_bonus_operation(
            user_id, self._claim_daily_bonus_internal, user_id, bonus_amount
        )

    async def process_referral_concurrent(self, referrer_id: int, referred_id: int, reward_amount: float) -> bool:
        """Process referral with concurrent operation safety"""
        return await self.concurrent_ops.execute_referral_operation(
            referrer_id, self._process_referral_internal, referrer_id, referred_id, reward_amount
        )

    async def batch_get_users(self, user_ids: List[int]) -> List[Optional[User]]:
        """Get multiple users concurrently with caching"""
        operations = []
        # Simplified batch operation - just get all users
        results = []
        for user_id in user_ids:
            user = await self.get_user(user_id)
            results.append(user)
        return results
