import os
from dotenv import load_dotenv
from typing import List, Optional
import logging

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

class Config:
    """Configuration class for the Telegram Referral Bot"""
    
    # Bot Configuration
    BOT_TOKEN = os.getenv('BOT_TOKEN')
    BOT_USERNAME = os.getenv('BOT_USERNAME')
    
    # Database Configuration
    MONGODB_URI = os.getenv('MONGODB_URI')
    DATABASE_NAME = os.getenv('DATABASE_NAME', 'referral_bot_db')
    
    # Admin Configuration
    ADMIN_USER_IDS = [int(id.strip()) for id in os.getenv('ADMIN_USER_IDS', '').split(',') if id.strip()]
    ADMIN_PASSWORD = os.getenv('ADMIN_PASSWORD', 'admin123')
    PURCHASE_LOG_CHANNEL_ID = int(os.getenv('PURCHASE_LOG_CHANNEL_ID', 0)) if os.getenv('PURCHASE_LOG_CHANNEL_ID') else None
    TASK_REVIEW_ADMIN_ID = int(os.getenv('TASK_REVIEW_ADMIN_ID', 0)) if os.getenv('TASK_REVIEW_ADMIN_ID') else None
    
    # Bot Settings
    REFERRAL_REWARD = int(os.getenv('REFERRAL_REWARD', 50))
    FRIEND_WELCOME_BONUS = int(os.getenv('FRIEND_WELCOME_BONUS', 25))
    
    # Security
    SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-change-this')
    RATE_LIMIT_MESSAGES = int(os.getenv('RATE_LIMIT_MESSAGES', 20))
    RATE_LIMIT_WINDOW = int(os.getenv('RATE_LIMIT_WINDOW', 60))

    # Broadcast Configuration
    BROADCAST_RATE_LIMIT = int(os.getenv('BROADCAST_RATE_LIMIT', 30))  # Messages per second (Telegram limit)
    BROADCAST_BATCH_SIZE = int(os.getenv('BROADCAST_BATCH_SIZE', 100))  # Users processed per batch
    BROADCAST_DELAY_BETWEEN_BATCHES = float(os.getenv('BROADCAST_DELAY_BETWEEN_BATCHES', 1.0))  # Seconds
    BROADCAST_MAX_RETRIES = int(os.getenv('BROADCAST_MAX_RETRIES', 3))  # Retry attempts for failed messages
    BROADCAST_RETRY_DELAY = float(os.getenv('BROADCAST_RETRY_DELAY', 5.0))  # Seconds between retries
    BROADCAST_PROGRESS_UPDATE_INTERVAL = int(os.getenv('BROADCAST_PROGRESS_UPDATE_INTERVAL', 50))  # Update admin every N messages
    BROADCAST_MEMORY_LIMIT_USERS = int(os.getenv('BROADCAST_MEMORY_LIMIT_USERS', 10000))  # Max users in memory at once
    BROADCAST_CLEANUP_FAILED_USERS = os.getenv('BROADCAST_CLEANUP_FAILED_USERS', 'true').lower() == 'true'
    BROADCAST_CLEANUP_BATCH_SIZE = int(os.getenv('BROADCAST_CLEANUP_BATCH_SIZE', 50))  # Users to cleanup per batch
    BROADCAST_ENABLE_RESUME = os.getenv('BROADCAST_ENABLE_RESUME', 'true').lower() == 'true'
    BROADCAST_LOG_LEVEL = os.getenv('BROADCAST_LOG_LEVEL', 'INFO')  # Separate log level for broadcast script
    
    # Logging
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', 'bot.log')
    
    # Channel Settings - Single Channel Forced Subscription Configuration
    REQUIRED_CHANNEL_ID = int(os.getenv('REQUIRED_CHANNEL_ID', '-1001296547211'))
    CHANNEL_INVITE_LINK = os.getenv('CHANNEL_INVITE_LINK', 'https://t.me/+ec_CC8b-gUxjNjQ1')

    # Legacy multi-channel settings (kept for backward compatibility)
    REQUIRED_CHANNEL_1 = int(os.getenv('REQUIRED_CHANNEL_1', '-1001296547211'))
    REQUIRED_CHANNEL_2 = int(os.getenv('REQUIRED_CHANNEL_2', '-1002414699235'))
    JOIN_LINK_1 = os.getenv('JOIN_LINK_1', 'https://t.me/+ec_CC8b-gUxjNjQ1')
    JOIN_LINK_2 = os.getenv('JOIN_LINK_2', 'https://t.me/+0vJ8rUZLPTE2ZDhl')
    REQUIRED_CHANNELS = [ch.strip() for ch in os.getenv('REQUIRED_CHANNELS', '').split(',') if ch.strip()]

    # Bot Mode - Always use long polling (no webhook configuration)
    USE_POLLING = True
    
    # Currency
    CURRENCY_SYMBOL = 'Genesis Tokens'
    CURRENCY_NAME = 'Genesis Tokens'
    
    # Bot Messages
    WELCOME_MESSAGE = """
🎉 Welcome to Genesis Bot! 🎉

🪙 Earn {referral_reward} Genesis Tokens for each successful referral!

Start earning now by inviting your friends! 🚀
    """.format(
        referral_reward=REFERRAL_REWARD
    )
    
    # Product Catalog (can be managed through admin panel)
    @classmethod
    def get_default_products(cls):
        """Get default products with dynamic pricing based on minimum withdrawal"""
        base_price = cls.MINIMUM_WITHDRAWAL * 5  # Products are 5x minimum withdrawal
        return [
            {
                'name': 'Canva Pro (1 Month)',
                'price': base_price,
                'description': 'Premium Canva subscription with all features unlocked',
                'category': 'Design Tools'
            },
            {
                'name': 'Spotify Premium (1 Month)',
                'price': base_price,
                'description': 'Ad-free music streaming with offline downloads',
                'category': 'Entertainment'
            },
            {
                'name': 'Netflix Premium (1 Month)',
                'price': base_price,
                'description': 'Premium Netflix subscription with 4K streaming',
                'category': 'Entertainment'
            },
            {
                'name': 'Adobe Creative Cloud (1 Month)',
                'price': base_price,
                'description': 'Access to all Adobe creative applications',
                'category': 'Design Tools'
            }
        ]

    # Backward compatibility - will be deprecated
    DEFAULT_PRODUCTS = []
    
    @classmethod
    def reload_config(cls):
        """Reload configuration from environment variables"""
        try:
            logger.info("🔄 Reloading configuration from environment variables...")

            # Reload .env file
            load_dotenv(override=True)

            # Update channel configuration
            cls.REQUIRED_CHANNEL_ID = int(os.getenv('REQUIRED_CHANNEL_ID', '-1001296547211'))
            cls.CHANNEL_INVITE_LINK = os.getenv('CHANNEL_INVITE_LINK', 'https://t.me/+ec_CC8b-gUxjNjQ1')

            # Legacy multi-channel configuration (for backward compatibility)
            cls.REQUIRED_CHANNEL_1 = int(os.getenv('REQUIRED_CHANNEL_1', '-1001296547211'))
            cls.REQUIRED_CHANNEL_2 = int(os.getenv('REQUIRED_CHANNEL_2', '-1002414699235'))
            cls.JOIN_LINK_1 = os.getenv('JOIN_LINK_1', 'https://t.me/+ec_CC8b-gUxjNjQ1')
            cls.JOIN_LINK_2 = os.getenv('JOIN_LINK_2', 'https://t.me/+0vJ8rUZLPTE2ZDhl')

            # Update other dynamic settings
            cls.REFERRAL_REWARD = int(os.getenv('REFERRAL_REWARD', 50))
            cls.FRIEND_WELCOME_BONUS = int(os.getenv('FRIEND_WELCOME_BONUS', 25))

            logger.info(f"✅ Configuration reloaded successfully")
            logger.info(f"📺 Channel Configuration (Single Channel):")
            logger.info(f"   - Required Channel: {cls.REQUIRED_CHANNEL_ID}")
            logger.info(f"   - Invite Link: {cls.CHANNEL_INVITE_LINK}")
            logger.info(f"💰 Financial Settings:")
            logger.info(f"   - Referral Reward: {cls.REFERRAL_REWARD} Genesis Tokens")
            logger.info(f"   - Friend Welcome Bonus: {cls.FRIEND_WELCOME_BONUS} Genesis Tokens")

            return True

        except Exception as e:
            logger.error(f"❌ Failed to reload configuration: {e}")
            return False



    @classmethod
    def get_required_channels(cls):
        """Get list of required channel IDs (single channel)"""
        return [cls.REQUIRED_CHANNEL_ID]

    @classmethod
    def get_join_links(cls):
        """Get list of join links (single link)"""
        return [cls.CHANNEL_INVITE_LINK]

    @classmethod
    def get_required_channel_id(cls):
        """Get the required channel ID"""
        return cls.REQUIRED_CHANNEL_ID

    @classmethod
    def get_channel_invite_link(cls):
        """Get the channel invite link"""
        return cls.CHANNEL_INVITE_LINK

    @classmethod
    def log_configuration(cls):
        """Log current configuration values for debugging (to file only)"""
        logger.debug("🔧 Current Configuration Values:")
        logger.debug("=" * 50)
        logger.debug(f"🤖 Bot Settings:")
        logger.debug(f"   - Bot Token: {'✅ Set' if cls.BOT_TOKEN else '❌ Missing'}")
        logger.debug(f"   - Bot Username: {cls.BOT_USERNAME or 'Not set'}")
        logger.debug(f"🗄️ Database:")
        logger.debug(f"   - MongoDB URI: {'✅ Set' if cls.MONGODB_URI else '❌ Missing'}")
        logger.debug(f"   - Database Name: {cls.DATABASE_NAME}")
        logger.debug(f"💰 Financial Settings:")
        logger.debug(f"   - Referral Reward: {cls.REFERRAL_REWARD} Genesis Tokens")
        logger.debug(f"   - Friend Welcome Bonus: {cls.FRIEND_WELCOME_BONUS} Genesis Tokens")
        logger.debug(f"📺 Channel (Single Channel):")
        logger.debug(f"   - Required Channel: {cls.REQUIRED_CHANNEL_ID}")
        logger.debug(f"🔗 Invite Link:")
        logger.debug(f"   - Link: {cls.CHANNEL_INVITE_LINK}")
        logger.debug("=" * 50)

    @classmethod
    def validate_config(cls):
        """Validate required configuration"""
        required_vars = ['BOT_TOKEN', 'MONGODB_URI']
        missing_vars = []

        for var in required_vars:
            if not getattr(cls, var):
                missing_vars.append(var)

        if missing_vars:
            raise ValueError(f"Missing required environment variables: {', '.join(missing_vars)}")

        # Validate channel configuration
        try:
            channel_id = cls.get_required_channel_id()
            invite_link = cls.get_channel_invite_link()

            if not channel_id:
                logger.warning("⚠️ Required channel ID not configured")

            if not invite_link:
                logger.warning("⚠️ Channel invite link not configured")

            logger.info(f"✅ Channel configuration validated: Channel ID {channel_id}, Invite link configured")

        except Exception as e:
            logger.error(f"❌ Channel configuration validation failed: {e}")

        return True
