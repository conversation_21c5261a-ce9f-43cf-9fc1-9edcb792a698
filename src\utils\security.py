"""
Security utilities for the Telegram Referral Bot
"""

import hashlib
import hmac
import secrets
import time
from typing import Dict, Optional, Set
from datetime import datetime, timedelta
import bcrypt
from config import Config
from .logger import log_security_event

class RateLimiter:
    """Enhanced rate limiting for high-concurrency user actions"""

    def __init__(self):
        self.user_requests: Dict[int, list] = {}
        self.global_requests: list = []
        self.cleanup_interval = 300  # 5 minutes
        self.last_cleanup = time.time()

        # Enhanced rate limiting settings for scalability
        self.global_rate_limit = 1000  # Global requests per minute
        self.burst_allowance = 10  # Burst requests allowed
        self.user_burst_window = 10  # Burst window in seconds
    
    def is_rate_limited(self, user_id: int, max_requests: int = None, window_seconds: int = None) -> bool:
        """Enhanced rate limiting with global and burst protection"""
        max_requests = max_requests or Config.RATE_LIMIT_MESSAGES
        window_seconds = window_seconds or Config.RATE_LIMIT_WINDOW

        current_time = time.time()

        # Cleanup old entries periodically
        if current_time - self.last_cleanup > self.cleanup_interval:
            self._cleanup_old_entries()

        # Check global rate limit first
        if self._is_global_rate_limited(current_time):
            log_security_event("GLOBAL_RATE_LIMIT_EXCEEDED", user_id, details="Global rate limit exceeded")
            return True

        # Get user's request history
        if user_id not in self.user_requests:
            self.user_requests[user_id] = []

        user_history = self.user_requests[user_id]

        # Remove requests outside the window
        cutoff_time = current_time - window_seconds
        user_history[:] = [req_time for req_time in user_history if req_time > cutoff_time]

        # Check burst protection
        burst_cutoff = current_time - self.user_burst_window
        recent_requests = [req_time for req_time in user_history if req_time > burst_cutoff]

        if len(recent_requests) >= self.burst_allowance:
            log_security_event("BURST_RATE_LIMIT_EXCEEDED", user_id,
                             details=f"Exceeded {self.burst_allowance} requests in {self.user_burst_window}s")
            return True

        # Check normal rate limit
        if len(user_history) >= max_requests:
            log_security_event("RATE_LIMIT_EXCEEDED", user_id,
                             details=f"Exceeded {max_requests} requests in {window_seconds}s")
            return True

        # Add current request
        user_history.append(current_time)
        self.global_requests.append(current_time)
        return False
    
    def _is_global_rate_limited(self, current_time: float) -> bool:
        """Check global rate limiting"""
        # Clean up old global requests
        cutoff_time = current_time - 60  # 1 minute window
        self.global_requests[:] = [req_time for req_time in self.global_requests if req_time > cutoff_time]

        return len(self.global_requests) >= self.global_rate_limit

    def _cleanup_old_entries(self):
        """Remove old entries to prevent memory leaks"""
        current_time = time.time()
        cutoff_time = current_time - (Config.RATE_LIMIT_WINDOW * 2)

        for user_id in list(self.user_requests.keys()):
            user_history = self.user_requests[user_id]
            user_history[:] = [req_time for req_time in user_history if req_time > cutoff_time]

            # Remove empty entries
            if not user_history:
                del self.user_requests[user_id]

        # Clean up global requests
        global_cutoff = current_time - 120  # 2 minutes buffer
        self.global_requests[:] = [req_time for req_time in self.global_requests if req_time > global_cutoff]

        self.last_cleanup = current_time

class SecurityValidator:
    """Security validation utilities"""
    
    @staticmethod
    def validate_referral_code(code: str) -> bool:
        """Validate referral code format"""
        if not code or len(code) != 8:
            return False
        
        # Check if code contains only alphanumeric characters
        return code.isalnum() and code.isupper()
    
    @staticmethod
    def validate_user_input(text: str, max_length: int = 1000) -> bool:
        """Validate user input for safety"""
        if not text:
            return False
        
        if len(text) > max_length:
            return False
        
        # Check for potentially malicious content
        dangerous_patterns = [
            '<script', '</script>', 'javascript:', 'data:',
            'vbscript:', 'onload=', 'onerror=', 'onclick='
        ]
        
        text_lower = text.lower()
        for pattern in dangerous_patterns:
            if pattern in text_lower:
                return False
        
        return True
    
    @staticmethod
    def sanitize_input(text: str) -> str:
        """Sanitize user input"""
        if not text:
            return ""
        
        # Remove potentially dangerous characters
        dangerous_chars = ['<', '>', '"', "'", '&', '\x00']
        for char in dangerous_chars:
            text = text.replace(char, '')
        
        # Limit length
        return text[:1000].strip()
    
    @staticmethod
    def hash_password(password: str) -> str:
        """Hash password using bcrypt"""
        salt = bcrypt.gensalt()
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        return hashed.decode('utf-8')
    
    @staticmethod
    def verify_password(password: str, hashed: str) -> bool:
        """Verify password against hash"""
        try:
            return bcrypt.checkpw(password.encode('utf-8'), hashed.encode('utf-8'))
        except Exception:
            return False
    
    @staticmethod
    def generate_secure_token(length: int = 32) -> str:
        """Generate secure random token"""
        return secrets.token_urlsafe(length)
    
    @staticmethod
    def verify_telegram_data(data: Dict, bot_token: str) -> bool:
        """Verify Telegram webhook data integrity"""
        try:
            # Extract hash from data
            received_hash = data.pop('hash', '')
            
            # Create data string
            data_check_string = '\n'.join([f"{k}={v}" for k, v in sorted(data.items())])
            
            # Create secret key
            secret_key = hashlib.sha256(bot_token.encode()).digest()
            
            # Calculate hash
            calculated_hash = hmac.new(secret_key, data_check_string.encode(), hashlib.sha256).hexdigest()
            
            return hmac.compare_digest(received_hash, calculated_hash)
        
        except Exception:
            return False

class BanManager:
    """Manage banned users and suspicious activities"""
    
    def __init__(self):
        self.suspicious_activities: Dict[int, list] = {}
        self.temp_bans: Dict[int, datetime] = {}
    
    def add_suspicious_activity(self, user_id: int, activity_type: str):
        """Add suspicious activity for user"""
        if user_id not in self.suspicious_activities:
            self.suspicious_activities[user_id] = []
        
        self.suspicious_activities[user_id].append({
            'type': activity_type,
            'timestamp': datetime.now()
        })
        
        log_security_event("SUSPICIOUS_ACTIVITY", user_id, details=activity_type)
        
        # Auto-ban if too many suspicious activities
        if len(self.suspicious_activities[user_id]) >= 5:
            self.temp_ban_user(user_id, hours=24)
    
    def temp_ban_user(self, user_id: int, hours: int = 24):
        """Temporarily ban user"""
        ban_until = datetime.now() + timedelta(hours=hours)
        self.temp_bans[user_id] = ban_until
        
        log_security_event("TEMP_BAN", user_id, details=f"Banned for {hours} hours")
    
    def is_temp_banned(self, user_id: int) -> bool:
        """Check if user is temporarily banned"""
        if user_id not in self.temp_bans:
            return False
        
        ban_until = self.temp_bans[user_id]
        if datetime.now() > ban_until:
            # Ban expired, remove it
            del self.temp_bans[user_id]
            return False
        
        return True
    
    def get_ban_time_remaining(self, user_id: int) -> Optional[timedelta]:
        """Get remaining ban time"""
        if user_id not in self.temp_bans:
            return None
        
        ban_until = self.temp_bans[user_id]
        remaining = ban_until - datetime.now()
        
        if remaining.total_seconds() <= 0:
            del self.temp_bans[user_id]
            return None
        
        return remaining

# Global instances
rate_limiter = RateLimiter()
ban_manager = BanManager()
security_validator = SecurityValidator()
